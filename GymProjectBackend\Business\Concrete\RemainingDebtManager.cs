﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Utilities.Results;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using Entities.DTOs;
using System.Collections.Generic;

namespace Business.Concrete
{
    public class RemainingDebtManager : IRemainingDebtService
    {
        private readonly IRemainingDebtDal _remainingDebtDal;
        private readonly ICompanyContext _companyContext;

        public RemainingDebtManager(IRemainingDebtDal remainingDebtDal, ICompanyContext companyContext)
        {
            _remainingDebtDal = remainingDebtDal;
            _companyContext = companyContext;
        }
        [PerformanceAspect(3)]
        [CacheAspect(300)] // 5 dakika cache - Borçlu üyeler detayları (kritik finansal data)
        public IDataResult<List<RemainingDebtDetailDto>> GetRemainingDebtDetails()
        {
            return new SuccessDataResult<List<RemainingDebtDetailDto>>(_remainingDebtDal.GetRemainingDebtDetails());
        }
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("RemainingDebt")]
        public IResult AddDebtPayment(DebtPaymentDto debtPaymentDto)
        {
            // SOLID prensiplerine uygun: Karmaşık business logic DAL katmanına devredildi
            return _remainingDebtDal.AddDebtPaymentWithBusinessLogic(debtPaymentDto);
        }

        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("RemainingDebt")]
        public IResult Delete(int remainingDebtId)
        {
            // SOLID prensiplerine uygun: Soft delete işlemini DAL katmanına devredildi
            return _remainingDebtDal.SoftDeleteRemainingDebt(remainingDebtId, _companyContext.GetCompanyId());
        }
    }
}