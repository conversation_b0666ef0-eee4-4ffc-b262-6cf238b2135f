﻿﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;

using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Validation;
using Core.Utilities.Results;
using Core.Utilities.Paging;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class MembershipTypeManager : IMembershipTypeService
    {
        IMembershiptypeDal _membershipTypeDal;
        private readonly ICompanyContext _companyContext;

        public MembershipTypeManager(IMembershiptypeDal membershipTypeDal, ICompanyContext companyContext)
        {
            _membershipTypeDal = membershipTypeDal;
            _companyContext = companyContext;
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [ValidationAspect(typeof(MembershipTypeValidator))]
        [CacheRemoveAspect("gym:{companyId}:imembershiptypeservice:*", "gym:{companyId}:imembershipservice:*")]
        public IResult Add(MembershipType membershipType)
        {
            _membershipTypeDal.Add(membershipType);
            return new SuccessResult(Messages.MembershipTypeAdded);
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [SmartCacheRemoveAspect("MembershipType")]
        public IResult Delete(int id)
        {
            // SOLID prensiplerine uygun: Complex business logic DAL'a devredildi
            return _membershipTypeDal.SoftDeleteMembershipType(id, _companyContext.GetCompanyId());
        }
        [CacheAspect(14400)] // 4 saat cache - Master data (üyelik türleri)
        public IDataResult<List<MembershipType>> GetAll()
        {
            return new SuccessDataResult<List<MembershipType>>(_membershipTypeDal.GetAll(m => m.IsActive == true));
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(7200)] // 2 saat cache - Pagination işlemleri
        public IDataResult<PaginatedResult<MembershipType>> GetAllPaginated(MembershipTypePagingParameters parameters)
        {
            var result = _membershipTypeDal.GetAllPaginated(parameters);
            return new SuccessDataResult<PaginatedResult<MembershipType>>(result);
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [PerformanceAspect(3)]
        [ValidationAspect(typeof(MembershipTypeValidator))]
        [CacheRemoveAspect("gym:{companyId}:imembershiptypeservice:*", "gym:{companyId}:imembershipservice:*")]
        public IResult Update(MembershipType membershipType)
        {
            // SOLID prensiplerine uygun: Complex business logic DAL'a devredildi
            return _membershipTypeDal.UpdateMembershipTypeWithBusinessLogic(membershipType, _companyContext.GetCompanyId());
        }
        [PerformanceAspect(3)]
        [SecuredOperation("owner,admin")]
        [CacheAspect(14400)] // 4 saat cache - Branch ve type bilgileri (master data)
        public IDataResult<List<BranchGetAllDto>> GetBranchesAndTypes()
        {
            // SOLID prensiplerine uygun: Karmaşık DTO mapping DAL katmanına devredildi
            var result = _membershipTypeDal.GetBranchesAndTypes();
            return new SuccessDataResult<List<BranchGetAllDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(7200)] // 2 saat cache - Branch bazlı paket bilgileri
        public IDataResult<List<PackageWithCountDto>> GetPackagesByBranch(string branch)
        {
            return new SuccessDataResult<List<PackageWithCountDto>>(_membershipTypeDal.GetPackagesByBranch(branch));
        }
    }
}
