{"test_results": {"api/member/getallpaginated": {"cache_duration": "600 seconds (10 minutes)", "tests": []}, "api/member/getmemberdetailspaginated": {"cache_duration": "1800 seconds (30 minutes)", "tests": []}, "api/remainingdebts": {"cache_duration": "300 seconds (5 minutes)", "tests": []}, "api/member/gettodayentries": {"cache_duration": "300 seconds (5 minutes)", "tests": []}, "api/membershipType/getall": {"cache_duration": "14400 seconds (4 hours)", "tests": []}}, "test_summary": {"total_tests": 0, "cache_hits": 0, "cache_misses": 0, "data_consistency_checks": 0, "invalidation_tests": 0}}