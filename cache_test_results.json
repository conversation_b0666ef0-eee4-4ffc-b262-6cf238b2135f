{"cache_test_results": {"test_date": "2025-07-30", "company_id_used": 1, "endpoints_tested": [{"endpoint": "api/member/getallpaginated", "cache_duration": "600s", "status": "completed", "cache_miss_hit_test": "passed", "data_consistency": "passed", "cache_key_pattern": "gym:1:imemberservice:getallpaginated:parameters:*"}, {"endpoint": "api/member/getmemberdetailspaginated", "cache_duration": "1800s", "status": "completed", "cache_miss_hit_test": "passed", "data_consistency": "passed", "cache_key_pattern": "gym:1:imemberservice:getmemberdetailspaginated:parameters:*"}, {"endpoint": "api/remainingdebts/getremainingdebtdetails", "cache_duration": "300s", "status": "completed", "cache_miss_hit_test": "passed", "data_consistency": "passed (minor JSON serialization differences)", "cache_key_pattern": "gym:1:iremainingdebtservice:getremainingdebtdetails:noparams", "cache_invalidation_test": "passed - SmartCacheRemoveAspect working correctly"}, {"endpoint": "api/member/gettodayentries?date=2025-06-11", "cache_duration": "300s", "status": "completed", "cache_miss_hit_test": "passed", "data_consistency": "passed", "cache_key_pattern": "gym:1:imemberservice:gettodayentries:date:*"}, {"endpoint": "api/membershipType/getall", "cache_duration": "14400s", "status": "completed", "cache_miss_hit_test": "passed", "data_consistency": "passed", "cache_key_pattern": "gym:1:imembershiptypeservice:getall:noparams"}], "cache_invalidation_test": {"method_used": "AddDebtPayment", "affected_cache": "RemainingDebt", "result": "passed", "details": "<PERSON><PERSON> successfully invalidated after debt payment, fresh data retrieved"}, "test_summary": {"total_endpoints_tested": 5, "cache_hits": 5, "cache_misses": 5, "data_consistency_checks": 5, "invalidation_tests": 1, "overall_result": "ALL TESTS PASSED"}}}